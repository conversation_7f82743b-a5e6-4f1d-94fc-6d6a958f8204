import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CreateEmployeeData } from '@/services/employeeService';
import { validateGhanaTIN, validateSSNIT } from '@/lib/utils';

interface AddEmployeeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateEmployeeData) => void;
}

export const AddEmployeeDialog: React.FC<AddEmployeeDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
}) => {
  const [formData, setFormData] = useState<CreateEmployeeData>({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    department: '',
    position: '',
    basic_salary: 0,
    housing_allowance: 0,
    transport_allowance: 0,
    other_allowances: 0,
    ssnit_number: '',
    tin_number: '',
    bank_name: '',
    bank_account: '',
    hire_date: new Date().toISOString().split('T')[0],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const departments = [
    'Human Resources',
    'Finance',
    'Engineering',
    'Marketing',
    'Sales',
    'Operations',
    'Customer Service',
    'Administration',
  ];

  const banks = [
    'Ghana Commercial Bank (GCB)',
    'Ecobank Ghana',
    'Standard Chartered Bank',
    'Absa Bank Ghana',
    'Fidelity Bank Ghana',
    'Zenith Bank Ghana',
    'Access Bank Ghana',
    'Stanbic Bank Ghana',
    'CAL Bank',
    'UMB Bank',
  ];

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.first_name.trim()) {
      newErrors.first_name = 'First name is required';
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = 'Last name is required';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    if (formData.basic_salary <= 0) {
      newErrors.basic_salary = 'Basic salary must be greater than 0';
    }

    if (formData.ssnit_number && !validateSSNIT(formData.ssnit_number)) {
      newErrors.ssnit_number = 'Invalid SSNIT format (should be S followed by 9 digits)';
    }

    if (formData.tin_number && !validateGhanaTIN(formData.tin_number)) {
      newErrors.tin_number = 'Invalid TIN format (should be 8-12 digits)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
      // Reset form
      setFormData({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        department: '',
        position: '',
        basic_salary: 0,
        housing_allowance: 0,
        transport_allowance: 0,
        other_allowances: 0,
        ssnit_number: '',
        tin_number: '',
        bank_name: '',
        bank_account: '',
        hire_date: new Date().toISOString().split('T')[0],
      });
      setErrors({});
    }
  };

  const handleInputChange = (field: keyof CreateEmployeeData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Employee</DialogTitle>
          <DialogDescription>
            Enter the employee's information to add them to the payroll system.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Personal Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Personal Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="first_name">First Name *</Label>
                <Input
                  id="first_name"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  placeholder="Enter first name"
                />
                {errors.first_name && (
                  <p className="text-sm text-red-500">{errors.first_name}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="last_name">Last Name *</Label>
                <Input
                  id="last_name"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  placeholder="Enter last name"
                />
                {errors.last_name && (
                  <p className="text-sm text-red-500">{errors.last_name}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+233 XX XXX XXXX"
                />
              </div>
            </div>
          </div>

          {/* Job Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Job Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Select
                  value={formData.department}
                  onValueChange={(value) => handleInputChange('department', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="position">Position</Label>
                <Input
                  id="position"
                  value={formData.position}
                  onChange={(e) => handleInputChange('position', e.target.value)}
                  placeholder="Job title"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="hire_date">Hire Date</Label>
              <Input
                id="hire_date"
                type="date"
                value={formData.hire_date}
                onChange={(e) => handleInputChange('hire_date', e.target.value)}
              />
            </div>
          </div>

          {/* Salary Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Salary Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="basic_salary">Basic Salary (₵) *</Label>
                <Input
                  id="basic_salary"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.basic_salary}
                  onChange={(e) => handleInputChange('basic_salary', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
                {errors.basic_salary && (
                  <p className="text-sm text-red-500">{errors.basic_salary}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="housing_allowance">Housing Allowance (₵)</Label>
                <Input
                  id="housing_allowance"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.housing_allowance}
                  onChange={(e) => handleInputChange('housing_allowance', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="transport_allowance">Transport Allowance (₵)</Label>
                <Input
                  id="transport_allowance"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.transport_allowance}
                  onChange={(e) => handleInputChange('transport_allowance', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="other_allowances">Other Allowances (₵)</Label>
                <Input
                  id="other_allowances"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.other_allowances}
                  onChange={(e) => handleInputChange('other_allowances', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
              </div>
            </div>
          </div>

          {/* Government & Banking Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Government & Banking Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ssnit_number">SSNIT Number</Label>
                <Input
                  id="ssnit_number"
                  value={formData.ssnit_number}
                  onChange={(e) => handleInputChange('ssnit_number', e.target.value)}
                  placeholder="S********9"
                />
                {errors.ssnit_number && (
                  <p className="text-sm text-red-500">{errors.ssnit_number}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="tin_number">TIN Number</Label>
                <Input
                  id="tin_number"
                  value={formData.tin_number}
                  onChange={(e) => handleInputChange('tin_number', e.target.value)}
                  placeholder="********"
                />
                {errors.tin_number && (
                  <p className="text-sm text-red-500">{errors.tin_number}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bank_name">Bank</Label>
                <Select
                  value={formData.bank_name}
                  onValueChange={(value) => handleInputChange('bank_name', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select bank" />
                  </SelectTrigger>
                  <SelectContent>
                    {banks.map((bank) => (
                      <SelectItem key={bank} value={bank}>
                        {bank}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="bank_account">Bank Account Number</Label>
                <Input
                  id="bank_account"
                  value={formData.bank_account}
                  onChange={(e) => handleInputChange('bank_account', e.target.value)}
                  placeholder="**********"
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">Add Employee</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import {
  Building2,
  Users,
  Calculator,
  Settings,
  Bell,
  User,
  Palette,
  LogOut,
  Check,
  X,
  AlertCircle,
  Info
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { NotificationDialog } from './dialogs/NotificationDialog';
import { SettingsDialog } from './dialogs/SettingsDialog';

interface HeaderProps {
  currentTab: string;
  onTabChange: (tab: string) => void;
}

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  timestamp: Date;
  read: boolean;
}

export const Header: React.FC<HeaderProps> = ({ currentTab, onTabChange }) => {
  const { profile, signOut } = useAuth();
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      title: 'Payroll Processing',
      message: 'January 2024 payroll is ready for approval',
      type: 'info',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      read: false
    },
    {
      id: '2',
      title: 'SSNIT Submission Due',
      message: 'SSNIT contributions for December 2023 are due in 3 days',
      type: 'warning',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      read: false
    },
    {
      id: '3',
      title: 'New Employee Added',
      message: 'Kwame Asante has been successfully added to the system',
      type: 'success',
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      read: true
    }
  ]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  
  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Building2 },
    { id: 'employees', label: 'Employees', icon: Users },
    { id: 'payroll', label: 'Payroll', icon: Calculator },
    { id: 'payslip-design', label: 'Payslip Design', icon: Palette },
  ];

  const handleSignOut = async () => {
    try {
      await signOut();
      toast({
        title: 'Signed out',
        description: 'You have been successfully signed out',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to sign out: ' + error.message,
        variant: 'destructive',
      });
    }
  };

  const handleNotificationClick = () => {
    setShowNotifications(true);
  };

  const handleSettingsClick = () => {
    setShowSettings(true);
  };

  const markNotificationAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllNotificationsAsRead = () => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.filter(notif => notif.id !== notificationId)
    );
  };

  const getUnreadCount = () => {
    return notifications.filter(notif => !notif.read).length;
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return Check;
      case 'warning': return AlertCircle;
      case 'error': return X;
      default: return Info;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  return (
    <header className="bg-card border-b border-border">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary rounded flex items-center justify-center">
              <Building2 className="w-5 h-5 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-foreground">PayrollGH</h1>
              <p className="text-xs text-muted-foreground">Ghana Payroll Platform</p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <Button
                  key={item.id}
                  variant={currentTab === item.id ? 'default' : 'ghost'}
                  onClick={() => onTabChange(item.id)}
                  className="flex items-center space-x-2"
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.label}</span>
                </Button>
              );
            })}
          </nav>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            <Badge variant="outline" className="text-ghana-green border-ghana-green">
              <div className="w-2 h-2 bg-ghana-green rounded-full mr-2"></div>
              Ghana Compliant
            </Badge>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                  <Bell className="w-5 h-5" />
                  {getUnreadCount() > 0 && (
                    <Badge className="absolute -top-1 -right-1 w-5 h-5 text-xs flex items-center justify-center p-0">
                      {getUnreadCount()}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel className="flex items-center justify-between">
                  <span>Notifications</span>
                  {getUnreadCount() > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={markAllNotificationsAsRead}
                      className="text-xs"
                    >
                      Mark all read
                    </Button>
                  )}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {notifications.length === 0 ? (
                  <DropdownMenuItem disabled>
                    <div className="text-center py-4 text-muted-foreground">
                      No notifications
                    </div>
                  </DropdownMenuItem>
                ) : (
                  notifications.slice(0, 5).map((notification) => {
                    const IconComponent = getNotificationIcon(notification.type);
                    return (
                      <DropdownMenuItem
                        key={notification.id}
                        className={`flex flex-col items-start space-y-1 p-3 ${!notification.read ? 'bg-muted/50' : ''}`}
                        onClick={() => markNotificationAsRead(notification.id)}
                      >
                        <div className="flex items-center space-x-2 w-full">
                          <IconComponent className={`w-4 h-4 ${
                            notification.type === 'success' ? 'text-green-500' :
                            notification.type === 'warning' ? 'text-yellow-500' :
                            notification.type === 'error' ? 'text-red-500' :
                            'text-blue-500'
                          }`} />
                          <span className="font-medium text-sm">{notification.title}</span>
                          <span className="text-xs text-muted-foreground ml-auto">
                            {formatTimeAgo(notification.timestamp)}
                          </span>
                        </div>
                        <p className="text-xs text-muted-foreground">{notification.message}</p>
                      </DropdownMenuItem>
                    );
                  })
                )}
                {notifications.length > 5 && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleNotificationClick}>
                      <span className="text-sm">View all notifications</span>
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <User className="w-5 h-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel className="flex flex-col items-start">
                  <span className="font-medium">
                    {profile?.first_name} {profile?.last_name}
                  </span>
                  <span className="text-sm text-muted-foreground capitalize">
                    {profile?.role}
                  </span>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSettingsClick}>
                  <Settings className="w-4 h-4 mr-2" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onTabChange('payslip-design')}>
                  <Palette className="w-4 h-4 mr-2" />
                  Payslip Design
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Dialogs */}
      <NotificationDialog
        open={showNotifications}
        onOpenChange={setShowNotifications}
        notifications={notifications}
        onMarkAsRead={markNotificationAsRead}
        onMarkAllAsRead={markAllNotificationsAsRead}
        onDelete={deleteNotification}
      />

      <SettingsDialog
        open={showSettings}
        onOpenChange={setShowSettings}
      />
    </header>
  );
};
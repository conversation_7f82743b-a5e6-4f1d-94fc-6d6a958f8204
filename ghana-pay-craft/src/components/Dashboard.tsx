import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { useState, useEffect } from "react";
import { PayrollService } from "@/services/payrollService";
import { EmployeeService } from "@/services/employeeService";
import { formatCurrency } from "@/lib/utils";
import {
  Users,
  DollarSign,
  FileCheck,
  Calendar,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Play,
  FileText
} from "lucide-react";

export const Dashboard = () => {
  const { profile } = useAuth();
  const { toast } = useToast();
  const [dashboardData, setDashboardData] = useState({
    totalEmployees: 0,
    monthlyPayroll: 0,
    ssnitContributions: 0,
    currentPayrollRun: null as any,
    employees: [] as any[]
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, [profile?.company_id]);

  const fetchDashboardData = async () => {
    if (!profile?.company_id) {
      setLoading(false);
      return;
    }

    try {
      // Use mock data for now since database tables might not exist
      const mockEmployees = [
        {
          id: '1',
          employee_id: 'EMP001',
          first_name: 'Kwame',
          last_name: 'Asante',
          basic_salary: 8000,
          housing_allowance: 2400,
          transport_allowance: 800,
          other_allowances: 450,
          status: 'active'
        },
        {
          id: '2',
          employee_id: 'EMP002',
          first_name: 'Ama',
          last_name: 'Osei',
          basic_salary: 6500,
          housing_allowance: 2000,
          transport_allowance: 600,
          other_allowances: 300,
          status: 'active'
        }
      ];

      const totalPayroll = mockEmployees.reduce((sum, emp) =>
        sum + emp.basic_salary + (emp.housing_allowance || 0) +
        (emp.transport_allowance || 0) + (emp.other_allowances || 0), 0
      );

      const ssnitTotal = mockEmployees.reduce((sum, emp) => {
        const gross = emp.basic_salary + (emp.housing_allowance || 0) +
                     (emp.transport_allowance || 0) + (emp.other_allowances || 0);
        return sum + (gross * 0.055); // 5.5% employee SSNIT
      }, 0);

      setDashboardData({
        totalEmployees: mockEmployees.length,
        monthlyPayroll: totalPayroll,
        ssnitContributions: ssnitTotal,
        currentPayrollRun: {
          id: '1',
          status: 'processing',
          total_gross: totalPayroll,
          total_deductions: ssnitTotal,
          total_net: totalPayroll - ssnitTotal,
          employee_count: mockEmployees.length
        },
        employees: mockEmployees
      });
    } catch (error: any) {
      console.error('Dashboard error:', error);
      // Set default data even on error
      setDashboardData({
        totalEmployees: 0,
        monthlyPayroll: 0,
        ssnitContributions: 0,
        currentPayrollRun: null,
        employees: []
      });
    } finally {
      setLoading(false);
    }
  };

  const handleContinueProcessing = async () => {
    if (!profile?.company_id) return;

    try {
      let payrollRun = dashboardData.currentPayrollRun;

      if (!payrollRun) {
        // Create new payroll run
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        payrollRun = await PayrollService.createPayrollRun(
          profile.company_id,
          startOfMonth.toISOString(),
          endOfMonth.toISOString()
        );
      }

      // Process payroll for active employees
      const activeEmployees = dashboardData.employees.filter(emp => emp.status === 'active');
      await PayrollService.processPayrollForEmployees(payrollRun.id, activeEmployees);
      await PayrollService.updatePayrollRunTotals(payrollRun.id);

      toast({
        title: 'Success',
        description: 'Payroll processing continued successfully',
      });

      fetchDashboardData();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to continue payroll processing: ' + error.message,
        variant: 'destructive',
      });
    }
  };

  const handleViewComplianceReports = () => {
    toast({
      title: 'Compliance Reports',
      description: 'Opening compliance reports dashboard...',
    });
    // In a real app, this would navigate to a compliance reports page
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.totalEmployees}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-ghana-green">Active employees</span>
            </p>
          </CardContent>
        </Card>
        
        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Payroll</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(dashboardData.monthlyPayroll)}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-ghana-green">Current month</span> estimate
            </p>
          </CardContent>
        </Card>
        
        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SSNIT Contributions</CardTitle>
            <FileCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(dashboardData.ssnitContributions)}</div>
            <p className="text-xs text-muted-foreground">
              Current month estimate
            </p>
          </CardContent>
        </Card>
        
        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Payroll</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Jan 31</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-ghana-gold">5 days</span> remaining
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Payroll Status */}
        <Card className="shadow-medium">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-primary" />
              <span>Current Payroll Run</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">January 2024 Payroll</p>
                <p className="text-sm text-muted-foreground">Period: Jan 1-31, 2024</p>
              </div>
              <Badge variant="secondary" className="bg-ghana-gold/10 text-ghana-gold border-ghana-gold/20">
                {dashboardData.currentPayrollRun ? dashboardData.currentPayrollRun.status : 'Not Started'}
              </Badge>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing Progress</span>
                <span>{dashboardData.currentPayrollRun ? '75%' : '0%'}</span>
              </div>
              <Progress value={dashboardData.currentPayrollRun ? 75 : 0} className="h-2" />
            </div>

            <div className="flex items-center justify-between pt-2">
              <div className="text-sm text-muted-foreground">
                {dashboardData.currentPayrollRun ?
                  `${Math.floor(dashboardData.totalEmployees * 0.75)} of ${dashboardData.totalEmployees} employees processed` :
                  `${dashboardData.totalEmployees} employees ready for processing`
                }
              </div>
              <Button size="sm" onClick={handleContinueProcessing}>
                <Play className="w-4 h-4 mr-1" />
                {dashboardData.currentPayrollRun ? 'Continue Processing' : 'Start Processing'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Ghana Compliance Status */}
        <Card className="shadow-medium">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-ghana-green" />
              <span>Ghana Compliance</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-ghana-green" />
                <div>
                  <p className="text-sm font-medium">SSNIT</p>
                  <p className="text-xs text-muted-foreground">Up to date</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-ghana-green" />
                <div>
                  <p className="text-sm font-medium">PAYE</p>
                  <p className="text-xs text-muted-foreground">Configured</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-4 h-4 text-ghana-gold" />
                <div>
                  <p className="text-sm font-medium">NHIS</p>
                  <p className="text-xs text-muted-foreground">Pending setup</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-ghana-green" />
                <div>
                  <p className="text-sm font-medium">Bank Integration</p>
                  <p className="text-xs text-muted-foreground">GCB Bank</p>
                </div>
              </div>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={handleViewComplianceReports}
            >
              <FileText className="w-4 h-4 mr-1" />
              View Compliance Reports
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card className="shadow-medium">
        <CardHeader>
          <CardTitle>Recent Payroll Activities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-ghana-green rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">December 2023 payroll completed</p>
                <p className="text-xs text-muted-foreground">247 employees paid • ₵2.1M total</p>
              </div>
              <span className="text-xs text-muted-foreground">2 days ago</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-ghana-gold rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">SSNIT contributions submitted</p>
                <p className="text-xs text-muted-foreground">December 2023 • ₵165K submitted</p>
              </div>
              <span className="text-xs text-muted-foreground">3 days ago</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">New employee added</p>
                <p className="text-xs text-muted-foreground">Kwame Asante • Software Engineer</p>
              </div>
              <span className="text-xs text-muted-foreground">1 week ago</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
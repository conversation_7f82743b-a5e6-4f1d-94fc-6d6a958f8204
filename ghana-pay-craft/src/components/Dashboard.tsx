import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  DollarSign, 
  FileCheck, 
  Calendar,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react";

export const Dashboard = () => {
  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">247</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-ghana-green">+12</span> from last month
            </p>
          </CardContent>
        </Card>
        
        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Payroll</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₵2.4M</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-ghana-green">+5.2%</span> from last month
            </p>
          </CardContent>
        </Card>
        
        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SSNIT Contributions</CardTitle>
            <FileCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₵186K</div>
            <p className="text-xs text-muted-foreground">
              Current month total
            </p>
          </CardContent>
        </Card>
        
        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Payroll</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Jan 31</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-ghana-gold">5 days</span> remaining
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Payroll Status */}
        <Card className="shadow-medium">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-primary" />
              <span>Current Payroll Run</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">January 2024 Payroll</p>
                <p className="text-sm text-muted-foreground">Period: Jan 1-31, 2024</p>
              </div>
              <Badge variant="secondary" className="bg-ghana-gold/10 text-ghana-gold border-ghana-gold/20">
                In Progress
              </Badge>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing Progress</span>
                <span>75%</span>
              </div>
              <Progress value={75} className="h-2" />
            </div>
            
            <div className="flex items-center justify-between pt-2">
              <div className="text-sm text-muted-foreground">
                185 of 247 employees processed
              </div>
              <Button size="sm">Continue Processing</Button>
            </div>
          </CardContent>
        </Card>

        {/* Ghana Compliance Status */}
        <Card className="shadow-medium">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-ghana-green" />
              <span>Ghana Compliance</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-ghana-green" />
                <div>
                  <p className="text-sm font-medium">SSNIT</p>
                  <p className="text-xs text-muted-foreground">Up to date</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-ghana-green" />
                <div>
                  <p className="text-sm font-medium">PAYE</p>
                  <p className="text-xs text-muted-foreground">Configured</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-4 h-4 text-ghana-gold" />
                <div>
                  <p className="text-sm font-medium">NHIS</p>
                  <p className="text-xs text-muted-foreground">Pending setup</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-ghana-green" />
                <div>
                  <p className="text-sm font-medium">Bank Integration</p>
                  <p className="text-xs text-muted-foreground">GCB Bank</p>
                </div>
              </div>
            </div>
            
            <Button variant="outline" size="sm" className="w-full">
              View Compliance Reports
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card className="shadow-medium">
        <CardHeader>
          <CardTitle>Recent Payroll Activities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-ghana-green rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">December 2023 payroll completed</p>
                <p className="text-xs text-muted-foreground">247 employees paid • ₵2.1M total</p>
              </div>
              <span className="text-xs text-muted-foreground">2 days ago</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-ghana-gold rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">SSNIT contributions submitted</p>
                <p className="text-xs text-muted-foreground">December 2023 • ₵165K submitted</p>
              </div>
              <span className="text-xs text-muted-foreground">3 days ago</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">New employee added</p>
                <p className="text-xs text-muted-foreground">Kwame Asante • Software Engineer</p>
              </div>
              <span className="text-xs text-muted-foreground">1 week ago</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
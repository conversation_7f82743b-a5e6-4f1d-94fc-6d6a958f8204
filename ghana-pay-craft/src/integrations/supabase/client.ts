// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ochvgasfovpnttvhjyuf.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9jaHZnYXNmb3ZwbnR0dmhqeXVmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyMjYyMzQsImV4cCI6MjA3MTgwMjIzNH0.66hI9M6gu-JfsOO2dIOuy4FTALOX4f5Ijk3v6IijCKw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});
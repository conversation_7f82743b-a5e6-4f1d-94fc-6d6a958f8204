// PDF generation service for payslips and reports
// Note: This is a simplified implementation. In production, you might want to use
// libraries like jsPDF, PDFKit, or a server-side PDF generation service

export interface PayslipData {
  employee: {
    first_name: string;
    last_name: string;
    employee_id: string;
    department?: string;
    position?: string;
    ssnit_number?: string;
    tin_number?: string;
    bank_name?: string;
    bank_account?: string;
  };
  company: {
    name: string;
    address: string;
    tin: string;
    logo?: string;
  };
  payroll: {
    period_start: string;
    period_end: string;
    basic_salary: number;
    housing_allowance: number;
    transport_allowance: number;
    other_allowances: number;
    overtime_amount: number;
    gross_salary: number;
    ssnit_employee: number;
    ssnit_employer: number;
    paye_tax: number;
    nhis: number;
    other_deductions: number;
    net_salary: number;
  };
  branding?: {
    primaryColor: string;
    secondaryColor: string;
    headerStyle: string;
    footerText: string;
  };
}

export class PDFService {
  static async generatePayslipPDF(data: PayslipData): Promise<Blob> {
    // This is a placeholder implementation
    // In a real application, you would use a PDF library like jsPDF
    
    const htmlContent = this.generatePayslipHTML(data);
    
    // For now, we'll create a simple text-based PDF simulation
    // In production, replace this with actual PDF generation
    const pdfBlob = new Blob([htmlContent], { type: 'application/pdf' });
    
    return pdfBlob;
  }

  static generatePayslipHTML(data: PayslipData): string {
    const { employee, company, payroll, branding } = data;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Payslip - ${employee.first_name} ${employee.last_name}</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            color: ${branding?.primaryColor || '#000'};
          }
          .header { 
            text-align: center; 
            border-bottom: 2px solid ${branding?.primaryColor || '#000'}; 
            padding-bottom: 20px; 
            margin-bottom: 20px; 
          }
          .company-name { 
            font-size: 24px; 
            font-weight: bold; 
            color: ${branding?.primaryColor || '#000'}; 
          }
          .payslip-title { 
            font-size: 18px; 
            margin-top: 10px; 
          }
          .employee-info { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 20px; 
            margin-bottom: 20px; 
          }
          .section { 
            margin-bottom: 20px; 
          }
          .section-title { 
            font-weight: bold; 
            font-size: 16px; 
            color: ${branding?.secondaryColor || '#666'}; 
            border-bottom: 1px solid #ccc; 
            padding-bottom: 5px; 
            margin-bottom: 10px; 
          }
          .earnings, .deductions { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 10px; 
          }
          .item { 
            display: flex; 
            justify-content: space-between; 
            padding: 5px 0; 
          }
          .total { 
            font-weight: bold; 
            border-top: 1px solid #ccc; 
            padding-top: 5px; 
            margin-top: 5px; 
          }
          .net-pay { 
            background-color: ${branding?.primaryColor || '#000'}10; 
            padding: 15px; 
            border-radius: 5px; 
            text-align: center; 
            font-size: 20px; 
            font-weight: bold; 
            margin: 20px 0; 
          }
          .footer { 
            text-align: center; 
            font-size: 12px; 
            color: #666; 
            border-top: 1px solid #ccc; 
            padding-top: 20px; 
            margin-top: 30px; 
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-name">${company.name}</div>
          <div>${company.address}</div>
          <div>TIN: ${company.tin}</div>
          <div class="payslip-title">PAYSLIP - ${new Date(payroll.period_start).toLocaleDateString('en-GB', { month: 'long', year: 'numeric' }).toUpperCase()}</div>
        </div>

        <div class="employee-info">
          <div>
            <strong>Employee:</strong> ${employee.first_name} ${employee.last_name}<br>
            <strong>ID:</strong> ${employee.employee_id}<br>
            <strong>Department:</strong> ${employee.department || 'N/A'}<br>
            <strong>Position:</strong> ${employee.position || 'N/A'}
          </div>
          <div>
            <strong>SSNIT No:</strong> ${employee.ssnit_number || 'N/A'}<br>
            <strong>TIN:</strong> ${employee.tin_number || 'N/A'}<br>
            <strong>Bank:</strong> ${employee.bank_name || 'N/A'}<br>
            <strong>Account:</strong> ${employee.bank_account || 'N/A'}
          </div>
        </div>

        <div class="section">
          <div class="section-title">EARNINGS</div>
          <div class="earnings">
            <div class="item">
              <span>Basic Salary</span>
              <span>₵${payroll.basic_salary.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
            </div>
            ${payroll.housing_allowance > 0 ? `
            <div class="item">
              <span>Housing Allowance</span>
              <span>₵${payroll.housing_allowance.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
            </div>
            ` : ''}
            ${payroll.transport_allowance > 0 ? `
            <div class="item">
              <span>Transport Allowance</span>
              <span>₵${payroll.transport_allowance.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
            </div>
            ` : ''}
            ${payroll.other_allowances > 0 ? `
            <div class="item">
              <span>Other Allowances</span>
              <span>₵${payroll.other_allowances.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
            </div>
            ` : ''}
            ${payroll.overtime_amount > 0 ? `
            <div class="item">
              <span>Overtime</span>
              <span>₵${payroll.overtime_amount.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
            </div>
            ` : ''}
          </div>
          <div class="item total">
            <span>Gross Pay</span>
            <span>₵${payroll.gross_salary.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
          </div>
        </div>

        <div class="section">
          <div class="section-title">DEDUCTIONS</div>
          <div class="deductions">
            <div class="item">
              <span>Employee SSNIT (5.5%)</span>
              <span>₵${payroll.ssnit_employee.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
            </div>
            <div class="item">
              <span>PAYE Tax</span>
              <span>₵${payroll.paye_tax.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
            </div>
            <div class="item">
              <span>NHIS (2.5%)</span>
              <span>₵${payroll.nhis.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
            </div>
            ${payroll.other_deductions > 0 ? `
            <div class="item">
              <span>Other Deductions</span>
              <span>₵${payroll.other_deductions.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
            </div>
            ` : ''}
          </div>
          <div class="item total">
            <span>Total Deductions</span>
            <span>₵${(payroll.ssnit_employee + payroll.paye_tax + payroll.nhis + payroll.other_deductions).toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
          </div>
        </div>

        <div class="net-pay">
          NET PAY: ₵${payroll.net_salary.toLocaleString('en-GH', { minimumFractionDigits: 2 })}
        </div>

        <div class="section">
          <div class="section-title">EMPLOYER CONTRIBUTIONS</div>
          <div class="item">
            <span>SSNIT (13%)</span>
            <span>₵${payroll.ssnit_employer.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</span>
          </div>
        </div>

        ${branding?.footerText ? `
        <div class="footer">
          ${branding.footerText}
        </div>
        ` : ''}
      </body>
      </html>
    `;
  }

  static async downloadPayslip(data: PayslipData): Promise<void> {
    const htmlContent = this.generatePayslipHTML(data);
    
    // Create a blob with the HTML content
    const blob = new Blob([htmlContent], { type: 'text/html' });
    
    // Create download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `payslip-${data.employee.employee_id}-${new Date(data.payroll.period_start).toISOString().slice(0, 7)}.html`;
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    URL.revokeObjectURL(url);
  }

  static async printPayslip(data: PayslipData): Promise<void> {
    const htmlContent = this.generatePayslipHTML(data);
    
    // Open print window
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(htmlContent);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }
  }

  static async generatePayrollReport(payrollData: any[]): Promise<Blob> {
    // Generate a summary report for the entire payroll
    const htmlContent = this.generatePayrollReportHTML(payrollData);
    const blob = new Blob([htmlContent], { type: 'text/html' });
    return blob;
  }

  private static generatePayrollReportHTML(payrollData: any[]): string {
    // Implementation for payroll summary report
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Payroll Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          table { width: 100%; border-collapse: collapse; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <h1>Payroll Summary Report</h1>
        <table>
          <thead>
            <tr>
              <th>Employee ID</th>
              <th>Name</th>
              <th>Gross Salary</th>
              <th>Deductions</th>
              <th>Net Salary</th>
            </tr>
          </thead>
          <tbody>
            ${payrollData.map(item => `
              <tr>
                <td>${item.employee_id}</td>
                <td>${item.first_name} ${item.last_name}</td>
                <td>₵${item.gross_salary.toLocaleString()}</td>
                <td>₵${(item.ssnit_employee + item.paye_tax + item.nhis).toLocaleString()}</td>
                <td>₵${item.net_salary.toLocaleString()}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;
  }
}

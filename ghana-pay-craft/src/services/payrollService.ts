import { supabase } from '@/integrations/supabase/client';
import { Employee } from './employeeService';
import { calculatePAYE, calculateSSNIT, calculateNHIS } from '@/lib/utils';

export interface PayrollRun {
  id?: string;
  company_id: string;
  period_start: string;
  period_end: string;
  status: 'draft' | 'processing' | 'completed' | 'approved';
  total_gross: number;
  total_deductions: number;
  total_net: number;
  employee_count: number;
  created_at?: string;
  updated_at?: string;
}

export interface PayrollItem {
  id?: string;
  payroll_run_id: string;
  employee_id: string;
  basic_salary: number;
  housing_allowance: number;
  transport_allowance: number;
  other_allowances: number;
  overtime_amount: number;
  gross_salary: number;
  ssnit_employee: number;
  ssnit_employer: number;
  paye_tax: number;
  nhis: number;
  other_deductions: number;
  net_salary: number;
  created_at?: string;
}

export class PayrollService {
  static async createPayrollRun(
    companyId: string,
    periodStart: string,
    periodEnd: string
  ): Promise<PayrollRun> {
    const { data, error } = await supabase
      .from('payroll_runs')
      .insert({
        company_id: companyId,
        period_start: periodStart,
        period_end: periodEnd,
        status: 'draft',
        total_gross: 0,
        total_deductions: 0,
        total_net: 0,
        employee_count: 0
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async getPayrollRuns(companyId: string): Promise<PayrollRun[]> {
    const { data, error } = await supabase
      .from('payroll_runs')
      .select('*')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getCurrentPayrollRun(companyId: string): Promise<PayrollRun | null> {
    try {
      const { data, error } = await supabase
        .from('payroll_runs')
        .select('*')
        .eq('company_id', companyId)
        .in('status', ['draft', 'processing'])
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.warn('Database table might not exist, returning null:', error.message);
        return null;
      }
      return data || null;
    } catch (error) {
      console.warn('Payroll service error, returning null:', error);
      return null;
    }
  }

  static async processPayrollForEmployees(
    payrollRunId: string,
    employees: Employee[]
  ): Promise<PayrollItem[]> {
    const payrollItems: PayrollItem[] = [];

    for (const employee of employees) {
      const grossSalary = 
        employee.basic_salary +
        (employee.housing_allowance || 0) +
        (employee.transport_allowance || 0) +
        (employee.other_allowances || 0);

      const ssnitEmployee = calculateSSNIT(grossSalary, true);
      const ssnitEmployer = calculateSSNIT(grossSalary, false);
      const paye = calculatePAYE(grossSalary);
      const nhis = calculateNHIS(grossSalary);
      
      const totalDeductions = ssnitEmployee + paye + nhis;
      const netSalary = grossSalary - totalDeductions;

      const payrollItem: PayrollItem = {
        payroll_run_id: payrollRunId,
        employee_id: employee.id!,
        basic_salary: employee.basic_salary,
        housing_allowance: employee.housing_allowance || 0,
        transport_allowance: employee.transport_allowance || 0,
        other_allowances: employee.other_allowances || 0,
        overtime_amount: 0, // Can be added later
        gross_salary: grossSalary,
        ssnit_employee: ssnitEmployee,
        ssnit_employer: ssnitEmployer,
        paye_tax: paye,
        nhis: nhis,
        other_deductions: 0, // Can be added later
        net_salary: netSalary
      };

      payrollItems.push(payrollItem);
    }

    const { data, error } = await supabase
      .from('payroll_items')
      .insert(payrollItems)
      .select();

    if (error) throw error;
    return data;
  }

  static async updatePayrollRunTotals(payrollRunId: string): Promise<PayrollRun> {
    // Get all payroll items for this run
    const { data: items, error: itemsError } = await supabase
      .from('payroll_items')
      .select('*')
      .eq('payroll_run_id', payrollRunId);

    if (itemsError) throw itemsError;

    const totalGross = items?.reduce((sum, item) => sum + item.gross_salary, 0) || 0;
    const totalDeductions = items?.reduce((sum, item) => 
      sum + item.ssnit_employee + item.paye_tax + item.nhis + item.other_deductions, 0) || 0;
    const totalNet = items?.reduce((sum, item) => sum + item.net_salary, 0) || 0;
    const employeeCount = items?.length || 0;

    const { data, error } = await supabase
      .from('payroll_runs')
      .update({
        total_gross: totalGross,
        total_deductions: totalDeductions,
        total_net: totalNet,
        employee_count: employeeCount
      })
      .eq('id', payrollRunId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async approvePayrollRun(payrollRunId: string): Promise<PayrollRun> {
    const { data, error } = await supabase
      .from('payroll_runs')
      .update({ status: 'approved' })
      .eq('id', payrollRunId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async getPayrollItems(payrollRunId: string): Promise<PayrollItem[]> {
    const { data, error } = await supabase
      .from('payroll_items')
      .select(`
        *,
        employee:employees(*)
      `)
      .eq('payroll_run_id', payrollRunId);

    if (error) throw error;
    return data || [];
  }

  static async generatePayslipData(payrollItemId: string) {
    const { data, error } = await supabase
      .from('payroll_items')
      .select(`
        *,
        employee:employees(*),
        payroll_run:payroll_runs(*)
      `)
      .eq('id', payrollItemId)
      .single();

    if (error) throw error;
    return data;
  }
}

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Employee } from '@/services/employeeService';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Edit, Save, X, User, Briefcase, DollarSign, CreditCard } from 'lucide-react';

interface ViewEmployeeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employee: Employee | null;
  onEdit: (employeeId: string, updates: Partial<Employee>) => void;
}

export const ViewEmployeeDialog: React.FC<ViewEmployeeDialogProps> = ({
  open,
  onOpenChange,
  employee,
  onEdit,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<Partial<Employee>>({});

  useEffect(() => {
    if (employee) {
      setEditData(employee);
    }
  }, [employee]);

  if (!employee) return null;

  const handleSave = () => {
    onEdit(employee.id!, editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData(employee);
    setIsEditing(false);
  };

  const grossSalary = 
    (employee.basic_salary || 0) +
    (employee.housing_allowance || 0) +
    (employee.transport_allowance || 0) +
    (employee.other_allowances || 0);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>{employee.first_name} {employee.last_name}</span>
              </DialogTitle>
              <DialogDescription>
                Employee ID: {employee.employee_id} • {employee.department || 'No Department'}
              </DialogDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Badge 
                variant={employee.status === 'active' ? 'default' : 'secondary'}
                className={employee.status === 'active' ? 'bg-green-500' : ''}
              >
                {employee.status}
              </Badge>
              {!isEditing ? (
                <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </Button>
              ) : (
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={handleCancel}>
                    <X className="w-4 h-4 mr-1" />
                    Cancel
                  </Button>
                  <Button size="sm" onClick={handleSave}>
                    <Save className="w-4 h-4 mr-1" />
                    Save
                  </Button>
                </div>
              )}
            </div>
          </div>
        </DialogHeader>

        <Tabs defaultValue="personal" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="personal">Personal</TabsTrigger>
            <TabsTrigger value="job">Job Details</TabsTrigger>
            <TabsTrigger value="salary">Salary</TabsTrigger>
            <TabsTrigger value="banking">Banking</TabsTrigger>
          </TabsList>

          <TabsContent value="personal" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="w-4 h-4" />
                  <span>Personal Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>First Name</Label>
                    {isEditing ? (
                      <Input
                        value={editData.first_name || ''}
                        onChange={(e) => setEditData(prev => ({ ...prev, first_name: e.target.value }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{employee.first_name}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>Last Name</Label>
                    {isEditing ? (
                      <Input
                        value={editData.last_name || ''}
                        onChange={(e) => setEditData(prev => ({ ...prev, last_name: e.target.value }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{employee.last_name}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Email</Label>
                    {isEditing ? (
                      <Input
                        type="email"
                        value={editData.email || ''}
                        onChange={(e) => setEditData(prev => ({ ...prev, email: e.target.value }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{employee.email || 'Not provided'}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>Phone</Label>
                    {isEditing ? (
                      <Input
                        value={editData.phone || ''}
                        onChange={(e) => setEditData(prev => ({ ...prev, phone: e.target.value }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{employee.phone || 'Not provided'}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Hire Date</Label>
                    <p className="text-sm font-medium">
                      {employee.hire_date ? formatDate(employee.hire_date) : 'Not provided'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label>Employee Since</Label>
                    <p className="text-sm font-medium">
                      {employee.created_at ? formatDate(employee.created_at) : 'Unknown'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="job" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Briefcase className="w-4 h-4" />
                  <span>Job Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Department</Label>
                    {isEditing ? (
                      <Input
                        value={editData.department || ''}
                        onChange={(e) => setEditData(prev => ({ ...prev, department: e.target.value }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{employee.department || 'Not assigned'}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>Position</Label>
                    {isEditing ? (
                      <Input
                        value={editData.position || ''}
                        onChange={(e) => setEditData(prev => ({ ...prev, position: e.target.value }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{employee.position || 'Not specified'}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>SSNIT Number</Label>
                    {isEditing ? (
                      <Input
                        value={editData.ssnit_number || ''}
                        onChange={(e) => setEditData(prev => ({ ...prev, ssnit_number: e.target.value }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{employee.ssnit_number || 'Not provided'}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>TIN Number</Label>
                    {isEditing ? (
                      <Input
                        value={editData.tin_number || ''}
                        onChange={(e) => setEditData(prev => ({ ...prev, tin_number: e.target.value }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{employee.tin_number || 'Not provided'}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="salary" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4" />
                  <span>Salary Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Basic Salary</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        value={editData.basic_salary || 0}
                        onChange={(e) => setEditData(prev => ({ ...prev, basic_salary: parseFloat(e.target.value) || 0 }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{formatCurrency(employee.basic_salary)}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>Housing Allowance</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        value={editData.housing_allowance || 0}
                        onChange={(e) => setEditData(prev => ({ ...prev, housing_allowance: parseFloat(e.target.value) || 0 }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{formatCurrency(employee.housing_allowance || 0)}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Transport Allowance</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        value={editData.transport_allowance || 0}
                        onChange={(e) => setEditData(prev => ({ ...prev, transport_allowance: parseFloat(e.target.value) || 0 }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{formatCurrency(employee.transport_allowance || 0)}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>Other Allowances</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        value={editData.other_allowances || 0}
                        onChange={(e) => setEditData(prev => ({ ...prev, other_allowances: parseFloat(e.target.value) || 0 }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{formatCurrency(employee.other_allowances || 0)}</p>
                    )}
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="flex justify-between items-center">
                    <Label className="text-lg">Gross Salary</Label>
                    <p className="text-lg font-bold text-green-600">{formatCurrency(grossSalary)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="banking" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CreditCard className="w-4 h-4" />
                  <span>Banking Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Bank Name</Label>
                    {isEditing ? (
                      <Input
                        value={editData.bank_name || ''}
                        onChange={(e) => setEditData(prev => ({ ...prev, bank_name: e.target.value }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{employee.bank_name || 'Not provided'}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>Account Number</Label>
                    {isEditing ? (
                      <Input
                        value={editData.bank_account || ''}
                        onChange={(e) => setEditData(prev => ({ ...prev, bank_account: e.target.value }))}
                      />
                    ) : (
                      <p className="text-sm font-medium">{employee.bank_account || 'Not provided'}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

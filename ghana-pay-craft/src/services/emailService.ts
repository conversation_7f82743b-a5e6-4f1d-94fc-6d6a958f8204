// Email service for sending payslips and notifications
// Note: This is a client-side simulation. In production, email sending
// should be handled by a backend service for security reasons.

export interface EmailTemplate {
  subject: string;
  htmlBody: string;
  textBody: string;
}

export interface PayslipEmailData {
  employee: {
    first_name: string;
    last_name: string;
    email: string;
    employee_id: string;
  };
  company: {
    name: string;
  };
  payroll: {
    period_start: string;
    period_end: string;
    net_salary: number;
  };
}

export class EmailService {
  static async sendPayslipEmail(
    emailData: PayslipEmailData,
    payslipBlob: Blob
  ): Promise<{ success: boolean; message: string }> {
    try {
      // In a real application, this would call a backend API
      // For now, we'll simulate the email sending process
      
      const template = this.generatePayslipEmailTemplate(emailData);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Log the email details (in production, this would be sent via backend)
      console.log('Email would be sent with:', {
        to: emailData.employee.email,
        subject: template.subject,
        body: template.htmlBody,
        attachment: payslipBlob
      });
      
      return {
        success: true,
        message: `Payslip sent successfully to ${emailData.employee.email}`
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to send payslip: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  static generatePayslipEmailTemplate(data: PayslipEmailData): EmailTemplate {
    const periodMonth = new Date(data.payroll.period_start).toLocaleDateString('en-GB', { 
      month: 'long', 
      year: 'numeric' 
    });

    const subject = `Your ${periodMonth} Payslip - ${data.company.name}`;
    
    const htmlBody = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { 
            font-family: Arial, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            max-width: 600px; 
            margin: 0 auto; 
            padding: 20px; 
          }
          .header { 
            background-color: #f8f9fa; 
            padding: 20px; 
            border-radius: 5px; 
            margin-bottom: 20px; 
          }
          .company-name { 
            font-size: 24px; 
            font-weight: bold; 
            color: #2c3e50; 
          }
          .content { 
            background-color: white; 
            padding: 20px; 
            border-radius: 5px; 
            border: 1px solid #e9ecef; 
          }
          .highlight { 
            background-color: #e8f5e8; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 20px 0; 
            text-align: center; 
          }
          .footer { 
            margin-top: 30px; 
            padding-top: 20px; 
            border-top: 1px solid #e9ecef; 
            font-size: 14px; 
            color: #6c757d; 
          }
          .button { 
            display: inline-block; 
            background-color: #007bff; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 10px 0; 
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-name">${data.company.name}</div>
          <div>Payroll Department</div>
        </div>
        
        <div class="content">
          <h2>Dear ${data.employee.first_name},</h2>
          
          <p>Your payslip for <strong>${periodMonth}</strong> is now available.</p>
          
          <div class="highlight">
            <h3>Net Pay: ₵${data.payroll.net_salary.toLocaleString('en-GH', { minimumFractionDigits: 2 })}</h3>
            <p>Employee ID: ${data.employee.employee_id}</p>
          </div>
          
          <p>Please find your detailed payslip attached to this email. If you have any questions about your payslip, please contact the HR department.</p>
          
          <p><strong>Important:</strong> Please keep this payslip for your records as it may be required for tax purposes or loan applications.</p>
          
          <div class="footer">
            <p>This is an automated message from the ${data.company.name} payroll system. Please do not reply to this email.</p>
            <p>If you need assistance, please contact <NAME_EMAIL> or call the HR helpline.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const textBody = `
Dear ${data.employee.first_name},

Your payslip for ${periodMonth} is now available.

Net Pay: ₵${data.payroll.net_salary.toLocaleString('en-GH', { minimumFractionDigits: 2 })}
Employee ID: ${data.employee.employee_id}

Please find your detailed payslip attached to this email. If you have any questions about your payslip, please contact the HR department.

Important: Please keep this payslip for your records as it may be required for tax purposes or loan applications.

This is an automated message from the ${data.company.name} payroll system. Please do not reply to this email.

If you need assistance, please contact <NAME_EMAIL> or call the HR helpline.

Best regards,
${data.company.name} Payroll Team
    `;

    return { subject, htmlBody, textBody };
  }

  static async sendBulkPayslips(
    emailDataList: PayslipEmailData[],
    payslipBlobs: Blob[]
  ): Promise<{ success: number; failed: number; errors: string[] }> {
    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    for (let i = 0; i < emailDataList.length; i++) {
      try {
        const result = await this.sendPayslipEmail(emailDataList[i], payslipBlobs[i]);
        if (result.success) {
          success++;
        } else {
          failed++;
          errors.push(`${emailDataList[i].employee.email}: ${result.message}`);
        }
        
        // Add delay between emails to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        failed++;
        errors.push(`${emailDataList[i].employee.email}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return { success, failed, errors };
  }

  static async sendTestEmail(
    recipientEmail: string,
    companyName: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const template = this.generateTestEmailTemplate(companyName);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('Test email would be sent to:', recipientEmail);
      
      return {
        success: true,
        message: `Test email sent successfully to ${recipientEmail}`
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to send test email: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  static generateTestEmailTemplate(companyName: string): EmailTemplate {
    const subject = `Test Email from ${companyName} Payroll System`;
    
    const htmlBody = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; text-align: center; }
          .content { background-color: white; padding: 20px; border-radius: 5px; border: 1px solid #e9ecef; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${companyName}</h1>
          <p>Payroll System Test Email</p>
        </div>
        <div class="content">
          <h2>Email Configuration Test</h2>
          <p>This is a test email to verify that your payroll system email configuration is working correctly.</p>
          <p>If you received this email, your email settings are properly configured and you can proceed with sending payslips to your employees.</p>
          <p><strong>Test completed successfully!</strong></p>
        </div>
      </body>
      </html>
    `;

    const textBody = `
${companyName} - Payroll System Test Email

Email Configuration Test

This is a test email to verify that your payroll system email configuration is working correctly.

If you received this email, your email settings are properly configured and you can proceed with sending payslips to your employees.

Test completed successfully!
    `;

    return { subject, htmlBody, textBody };
  }

  static async sendNotificationEmail(
    recipientEmail: string,
    subject: string,
    message: string,
    companyName: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('Notification email would be sent:', {
        to: recipientEmail,
        subject,
        message,
        from: companyName
      });
      
      return {
        success: true,
        message: `Notification sent successfully to ${recipientEmail}`
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to send notification: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static async schedulePayslipDelivery(
    emailDataList: PayslipEmailData[],
    scheduledDate: Date
  ): Promise<{ success: boolean; message: string }> {
    try {
      // In production, this would schedule emails via a backend service
      console.log('Payslips scheduled for delivery:', {
        count: emailDataList.length,
        scheduledDate,
        recipients: emailDataList.map(data => data.employee.email)
      });
      
      return {
        success: true,
        message: `${emailDataList.length} payslips scheduled for delivery on ${scheduledDate.toLocaleDateString()}`
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to schedule payslips: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}

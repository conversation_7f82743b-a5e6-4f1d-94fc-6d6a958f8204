import { supabase } from '@/integrations/supabase/client';
import { generateEmployeeId } from '@/lib/utils';

export interface Employee {
  id?: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  department?: string;
  position?: string;
  basic_salary: number;
  housing_allowance?: number;
  transport_allowance?: number;
  other_allowances?: number;
  ssnit_number?: string;
  tin_number?: string;
  bank_name?: string;
  bank_account?: string;
  status: 'active' | 'inactive' | 'terminated';
  hire_date?: string;
  company_id: string;
  created_at?: string;
  updated_at?: string;
}

export interface CreateEmployeeData {
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  department?: string;
  position?: string;
  basic_salary: number;
  housing_allowance?: number;
  transport_allowance?: number;
  other_allowances?: number;
  ssnit_number?: string;
  tin_number?: string;
  bank_name?: string;
  bank_account?: string;
  hire_date?: string;
}

export class EmployeeService {
  static async createEmployee(companyId: string, employeeData: CreateEmployeeData): Promise<Employee> {
    const employee_id = generateEmployeeId();
    
    const { data, error } = await supabase
      .from('employees')
      .insert({
        ...employeeData,
        employee_id,
        company_id: companyId,
        status: 'active'
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async getEmployees(companyId: string): Promise<Employee[]> {
    const { data, error } = await supabase
      .from('employees')
      .select('*')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getEmployee(id: string): Promise<Employee | null> {
    const { data, error } = await supabase
      .from('employees')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  static async updateEmployee(id: string, updates: Partial<Employee>): Promise<Employee> {
    const { data, error } = await supabase
      .from('employees')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteEmployee(id: string): Promise<void> {
    const { error } = await supabase
      .from('employees')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async searchEmployees(companyId: string, searchTerm: string): Promise<Employee[]> {
    const { data, error } = await supabase
      .from('employees')
      .select('*')
      .eq('company_id', companyId)
      .or(`first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,employee_id.ilike.%${searchTerm}%,department.ilike.%${searchTerm}%`)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getEmployeesByDepartment(companyId: string, department: string): Promise<Employee[]> {
    const { data, error } = await supabase
      .from('employees')
      .select('*')
      .eq('company_id', companyId)
      .eq('department', department)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getEmployeesByStatus(companyId: string, status: string): Promise<Employee[]> {
    const { data, error } = await supabase
      .from('employees')
      .select('*')
      .eq('company_id', companyId)
      .eq('status', status)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static calculateGrossSalary(employee: Employee): number {
    return (
      employee.basic_salary +
      (employee.housing_allowance || 0) +
      (employee.transport_allowance || 0) +
      (employee.other_allowances || 0)
    );
  }

  static calculateNetSalary(employee: Employee): number {
    const grossSalary = this.calculateGrossSalary(employee);
    // This would include all deductions - simplified for now
    const ssnit = grossSalary * 0.055; // 5.5% employee SSNIT
    const nhis = grossSalary * 0.025; // 2.5% NHIS
    // PAYE calculation would be more complex
    const paye = grossSalary > 365 ? (grossSalary - 365) * 0.05 : 0; // Simplified
    
    return grossSalary - ssnit - nhis - paye;
  }
}

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { CompanyBranding } from "@/types/branding";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { useState, useEffect } from "react";
import { PayrollService } from "@/services/payrollService";
import { EmployeeService } from "@/services/employeeService";
import { PDFService } from "@/services/pdfService";
import { EmailService } from "@/services/emailService";
import { formatCurrency } from "@/lib/utils";
import {
  FileText,
  Download,
  Send,
  Eye,
  User,
  Banknote,
  CheckCircle,
  Mail
} from "lucide-react";

interface PayrollPreviewProps {
  branding?: CompanyBranding;
}

export const PayrollPreview = ({ branding }: PayrollPreviewProps) => {
  const { profile } = useAuth();
  const { toast } = useToast();
  const [payrollData, setPayrollData] = useState<any>(null);
  const [employees, setEmployees] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  const defaultBranding: CompanyBranding = {
    companyName: "ACME CORP GHANA LTD",
    address: "P.O. Box 123, Accra",
    tin: "********",
    primaryColor: "#CE1126",
    secondaryColor: "#FFD700",
    headerStyle: 'corporate',
    includeWatermark: false,
    footerText: "This payslip is computer generated and does not require signature."
  };

  const activeBranding = branding || defaultBranding;

  useEffect(() => {
    fetchPayrollData();
  }, [profile?.company_id]);

  const fetchPayrollData = async () => {
    const companyId = profile?.company_id || profile?.id || 'demo-company';
    if (!companyId) {
      setLoading(false);
      return;
    }

    try {
      // Use mock data for now since database tables might not exist
      const mockEmployees = [
        {
          id: '1',
          employee_id: 'EMP001',
          first_name: 'Kwame',
          last_name: 'Asante',
          basic_salary: 8000,
          housing_allowance: 2400,
          transport_allowance: 800,
          other_allowances: 450,
          status: 'active'
        },
        {
          id: '2',
          employee_id: 'EMP002',
          first_name: 'Ama',
          last_name: 'Osei',
          basic_salary: 6500,
          housing_allowance: 2000,
          transport_allowance: 600,
          other_allowances: 300,
          status: 'active'
        }
      ];

      const totalGross = mockEmployees.reduce((sum, emp) =>
        sum + emp.basic_salary + (emp.housing_allowance || 0) +
        (emp.transport_allowance || 0) + (emp.other_allowances || 0), 0
      );

      const totalDeductions = totalGross * 0.15; // Simplified calculation
      const totalNet = totalGross - totalDeductions;

      const mockPayrollData = {
        id: '1',
        status: 'draft',
        total_gross: totalGross,
        total_deductions: totalDeductions,
        total_net: totalNet,
        employee_count: mockEmployees.length,
        period_start: new Date().toISOString(),
        period_end: new Date().toISOString()
      };

      setPayrollData(mockPayrollData);
      setEmployees(mockEmployees);
    } catch (error: any) {
      console.error('Payroll fetch error:', error);
      setPayrollData(null);
      setEmployees([]);
    } finally {
      setLoading(false);
    }
  };

  const handleApprovePayroll = async () => {
    if (!payrollData?.id) {
      toast({
        title: 'Error',
        description: 'No payroll run to approve',
        variant: 'destructive',
      });
      return;
    }

    setProcessing(true);
    try {
      await PayrollService.approvePayrollRun(payrollData.id);
      toast({
        title: 'Success',
        description: 'Payroll approved successfully',
      });
      fetchPayrollData();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to approve payroll: ' + error.message,
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  const handlePreviewAll = () => {
    toast({
      title: 'Preview All',
      description: 'Opening payslip preview for all employees...',
    });
    // In a real app, this would open a modal or new page with all payslips
  };

  const handleDownloadPDF = async () => {
    if (!payrollData || employees.length === 0) {
      toast({
        title: 'Error',
        description: 'No payroll data available for download',
        variant: 'destructive',
      });
      return;
    }

    setProcessing(true);
    try {
      // Generate sample payslip for first employee
      const sampleEmployee = employees[0];
      const payslipData = {
        employee: {
          first_name: sampleEmployee.first_name,
          last_name: sampleEmployee.last_name,
          employee_id: sampleEmployee.employee_id,
          department: sampleEmployee.department,
          position: sampleEmployee.position,
          ssnit_number: sampleEmployee.ssnit_number,
          tin_number: sampleEmployee.tin_number,
          bank_name: sampleEmployee.bank_name,
          bank_account: sampleEmployee.bank_account,
        },
        company: {
          name: activeBranding.companyName,
          address: activeBranding.address,
          tin: activeBranding.tin,
          logo: activeBranding.logo,
        },
        payroll: {
          period_start: payrollData.period_start,
          period_end: payrollData.period_end,
          basic_salary: sampleEmployee.basic_salary,
          housing_allowance: sampleEmployee.housing_allowance || 0,
          transport_allowance: sampleEmployee.transport_allowance || 0,
          other_allowances: sampleEmployee.other_allowances || 0,
          overtime_amount: 0,
          gross_salary: sampleEmployee.basic_salary + (sampleEmployee.housing_allowance || 0) +
                       (sampleEmployee.transport_allowance || 0) + (sampleEmployee.other_allowances || 0),
          ssnit_employee: (sampleEmployee.basic_salary * 0.055),
          ssnit_employer: (sampleEmployee.basic_salary * 0.13),
          paye_tax: 0, // Simplified
          nhis: (sampleEmployee.basic_salary * 0.025),
          other_deductions: 0,
          net_salary: sampleEmployee.basic_salary * 0.8, // Simplified calculation
        },
        branding: activeBranding,
      };

      await PDFService.downloadPayslip(payslipData);

      toast({
        title: 'Success',
        description: 'Sample payslip downloaded successfully',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to download PDF: ' + error.message,
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleSendTest = async () => {
    if (!profile?.email) {
      toast({
        title: 'Error',
        description: 'No email address found for test',
        variant: 'destructive',
      });
      return;
    }

    setProcessing(true);
    try {
      const result = await EmailService.sendTestEmail(profile.email, activeBranding.companyName);

      if (result.success) {
        toast({
          title: 'Success',
          description: result.message,
        });
      } else {
        toast({
          title: 'Error',
          description: result.message,
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to send test email: ' + error.message,
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading payroll data...</p>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Payroll Preview</h2>
          <p className="text-muted-foreground">January 2024 • Preview Mode</p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="bg-ghana-gold/10 text-ghana-gold border-ghana-gold/20">
            {payrollData?.status || 'Draft'}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={handlePreviewAll}
            disabled={processing}
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview All
          </Button>
          <Button
            onClick={handleApprovePayroll}
            disabled={processing || !payrollData || payrollData.status === 'approved'}
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            {payrollData?.status === 'approved' ? 'Approved' : 'Approve Payroll'}
          </Button>
        </div>
      </div>

      {/* Payroll Summary */}
      <Card className="shadow-medium">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Banknote className="w-5 h-5 text-primary" />
            <span>Payroll Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">
                {formatCurrency(payrollData?.total_gross || 0)}
              </div>
              <div className="text-sm text-muted-foreground">Gross Pay</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-ghana-red">
                {formatCurrency(payrollData?.total_deductions || 0)}
              </div>
              <div className="text-sm text-muted-foreground">Total Deductions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-ghana-green">
                {formatCurrency(payrollData?.total_net || 0)}
              </div>
              <div className="text-sm text-muted-foreground">Net Pay</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">
                {payrollData?.employee_count || employees.length}
              </div>
              <div className="text-sm text-muted-foreground">Employees</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sample Payslip */}
      <Card className="shadow-medium">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-primary" />
              <span>Sample Payslip Preview</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownloadPDF}
                disabled={processing || employees.length === 0}
              >
                <Download className="w-4 h-4 mr-2" />
                Download PDF
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSendTest}
                disabled={processing}
              >
                <Mail className="w-4 h-4 mr-2" />
                Send Test
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-card border rounded-lg p-6 space-y-4">
            {/* Payslip Header */}
            <div className={activeBranding.headerStyle === 'modern' 
              ? "text-center pb-4 bg-gradient-primary text-white p-6 rounded-t-lg -m-6 mb-6"
              : activeBranding.headerStyle === 'corporate'
              ? "flex items-center justify-between border-b pb-4"
              : "text-center border-b pb-4"
            }>
              <div className={activeBranding.headerStyle === 'corporate' ? "" : "text-center"}>
                <h3 className="text-lg font-bold" style={{ color: activeBranding.headerStyle === 'modern' ? 'white' : activeBranding.primaryColor }}>
                  {activeBranding.companyName}
                </h3>
                <p className="text-sm text-muted-foreground">{activeBranding.address} • TIN: {activeBranding.tin}</p>
                <p className="text-sm font-medium mt-2">PAYSLIP - JANUARY 2024</p>
              </div>
            </div>

            {/* Employee Info */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p><strong>Employee:</strong> Kwame Asante</p>
                <p><strong>ID:</strong> EMP001</p>
                <p><strong>Department:</strong> Engineering</p>
              </div>
              <div>
                <p><strong>SSNIT No:</strong> S********9</p>
                <p><strong>TIN:</strong> T987654321</p>
                <p><strong>Bank:</strong> GCB - ********90</p>
              </div>
            </div>

            <Separator />

            {/* Earnings */}
            <div className="space-y-2">
              <h4 className="font-semibold text-ghana-green">EARNINGS</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>Basic Salary</span>
                  <span>₵8,000.00</span>
                </div>
                <div className="flex justify-between">
                  <span>Housing Allowance</span>
                  <span>₵2,400.00</span>
                </div>
                <div className="flex justify-between">
                  <span>Transport Allowance</span>
                  <span>₵800.00</span>
                </div>
                <div className="flex justify-between">
                  <span>Overtime</span>
                  <span>₵450.00</span>
                </div>
              </div>
              <div className="flex justify-between font-semibold border-t pt-2">
                <span>Gross Pay</span>
                <span>₵11,650.00</span>
              </div>
            </div>

            <Separator />

            {/* Deductions */}
            <div className="space-y-2">
              <h4 className="font-semibold text-ghana-red">DEDUCTIONS</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>Employee SSNIT (5.5%)</span>
                  <span>₵640.75</span>
                </div>
                <div className="flex justify-between">
                  <span>PAYE Tax</span>
                  <span>₵1,245.50</span>
                </div>
                <div className="flex justify-between">
                  <span>NHIS</span>
                  <span>₵116.50</span>
                </div>
                <div className="flex justify-between">
                  <span>Loan Repayment</span>
                  <span>₵500.00</span>
                </div>
              </div>
              <div className="flex justify-between font-semibold border-t pt-2">
                <span>Total Deductions</span>
                <span>₵2,502.75</span>
              </div>
            </div>

            <Separator />

            {/* Net Pay */}
            <div className="bg-primary/5 border border-primary/20 rounded-lg p-3">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">NET PAY</span>
                <span className="text-2xl font-bold" style={{ color: activeBranding.primaryColor }}>₵9,147.25</span>
              </div>
            </div>

            {/* Employer Contributions */}
            <div className="text-xs text-muted-foreground space-y-1">
              <p><strong>Employer Contributions:</strong></p>
              <p>SSNIT (13%): ₵1,514.50 | Pension (13%): ₵1,514.50</p>
            </div>

            {/* Footer */}
            {activeBranding.footerText && (
              <div className="text-xs text-muted-foreground text-center border-t pt-4">
                {activeBranding.footerText}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { Plus, Search, Filter, Users, Eye, Edit, Trash2 } from 'lucide-react';
import { EmployeeService, Employee } from '@/services/employeeService';
import { AddEmployeeDialog } from './dialogs/AddEmployeeDialog';
import { ViewEmployeeDialog } from './dialogs/ViewEmployeeDialog';
import { FilterEmployeeDialog } from './dialogs/FilterEmployeeDialog';

export const EmployeeManagement = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [showFilterDialog, setShowFilterDialog] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [filters, setFilters] = useState({
    department: '',
    status: '',
    position: ''
  });
  const { profile } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    fetchEmployees();
  }, [profile?.company_id]);

  const fetchEmployees = async () => {
    if (!profile?.company_id) {
      setLoading(false);
      return;
    }

    try {
      // Use mock data for now since database tables might not exist
      const mockEmployees = [
        {
          id: '1',
          employee_id: 'EMP001',
          first_name: 'Kwame',
          last_name: 'Asante',
          email: '<EMAIL>',
          department: 'Engineering',
          position: 'Software Engineer',
          basic_salary: 8000,
          housing_allowance: 2400,
          transport_allowance: 800,
          other_allowances: 450,
          status: 'active',
          company_id: profile.company_id,
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          employee_id: 'EMP002',
          first_name: 'Ama',
          last_name: 'Osei',
          email: '<EMAIL>',
          department: 'Finance',
          position: 'Financial Analyst',
          basic_salary: 6500,
          housing_allowance: 2000,
          transport_allowance: 600,
          other_allowances: 300,
          status: 'active',
          company_id: profile.company_id,
          created_at: new Date().toISOString()
        },
        {
          id: '3',
          employee_id: 'EMP003',
          first_name: 'Kofi',
          last_name: 'Mensah',
          email: '<EMAIL>',
          department: 'Marketing',
          position: 'Marketing Manager',
          basic_salary: 7500,
          housing_allowance: 2200,
          transport_allowance: 700,
          other_allowances: 400,
          status: 'active',
          company_id: profile.company_id,
          created_at: new Date().toISOString()
        }
      ];

      setEmployees(mockEmployees);
    } catch (error: any) {
      console.error('Employee fetch error:', error);
      setEmployees([]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddEmployee = async (employeeData: any) => {
    if (!profile?.company_id) return;

    try {
      // For now, just add to local state since database might not be set up
      const newEmployee = {
        id: Date.now().toString(),
        employee_id: `EMP${String(employees.length + 1).padStart(3, '0')}`,
        ...employeeData,
        company_id: profile.company_id,
        status: 'active',
        created_at: new Date().toISOString()
      };

      setEmployees(prev => [newEmployee, ...prev]);

      toast({
        title: 'Success',
        description: 'Employee added successfully',
      });
      setShowAddDialog(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to add employee: ' + error.message,
        variant: 'destructive',
      });
    }
  };

  const handleViewEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setShowViewDialog(true);
  };

  const handleEditEmployee = async (employeeId: string, updates: Partial<Employee>) => {
    try {
      // Update local state for now
      setEmployees(prev =>
        prev.map(emp =>
          emp.id === employeeId ? { ...emp, ...updates } : emp
        )
      );

      toast({
        title: 'Success',
        description: 'Employee updated successfully',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to update employee: ' + error.message,
        variant: 'destructive',
      });
    }
  };

  const handleDeleteEmployee = async (employeeId: string) => {
    if (!confirm('Are you sure you want to delete this employee?')) return;

    try {
      // Remove from local state for now
      setEmployees(prev => prev.filter(emp => emp.id !== employeeId));

      toast({
        title: 'Success',
        description: 'Employee deleted successfully',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to delete employee: ' + error.message,
        variant: 'destructive',
      });
    }
  };

  const handleApplyFilters = (newFilters: any) => {
    setFilters(newFilters);
    setShowFilterDialog(false);
  };

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch =
      `${employee.first_name} ${employee.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.employee_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (employee.department && employee.department.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesDepartment = !filters.department || employee.department === filters.department;
    const matchesStatus = !filters.status || employee.status === filters.status;
    const matchesPosition = !filters.position || employee.position === filters.position;

    return matchesSearch && matchesDepartment && matchesStatus && matchesPosition;
  });

  const formatSalary = (amount: number) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Employee Management</h2>
            <p className="text-muted-foreground">Manage employee profiles and payroll data</p>
          </div>
        </div>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading employees...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Employee Management</h2>
          <p className="text-muted-foreground">Manage employee profiles and payroll data</p>
        </div>
        <Button
          className="bg-primary hover:bg-primary/90"
          onClick={() => setShowAddDialog(true)}
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Employee
        </Button>
      </div>
      
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            type="text"
            placeholder="Search employees..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilterDialog(true)}
        >
          <Filter className="w-4 h-4 mr-2" />
          Filter
        </Button>
      </div>

      {employees.length === 0 ? (
        <Card className="shadow-medium">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Users className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No employees found</h3>
            <p className="text-muted-foreground text-center mb-6">
              Get started by adding your first employee to the system.
            </p>
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Add First Employee
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card className="shadow-medium">
          <CardHeader>
            <CardTitle>
              Active Employees ({filteredEmployees.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredEmployees.map((employee) => (
                <div key={employee.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold text-primary">
                        {employee.first_name[0]}{employee.last_name[0]}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">
                        {employee.first_name} {employee.last_name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {employee.employee_id} • {employee.department || 'No Department'}
                      </p>
                      {employee.position && (
                        <p className="text-xs text-muted-foreground">{employee.position}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="font-medium">{formatSalary(employee.basic_salary)}</p>
                      <Badge 
                        variant={employee.status === 'active' ? 'default' : 'secondary'}
                        className={employee.status === 'active' ? 'bg-ghana-green text-white' : ''}
                      >
                        {employee.status}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewEmployee(employee)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteEmployee(employee.id!)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dialogs */}
      <AddEmployeeDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onSubmit={handleAddEmployee}
      />

      <ViewEmployeeDialog
        open={showViewDialog}
        onOpenChange={setShowViewDialog}
        employee={selectedEmployee}
        onEdit={handleEditEmployee}
      />

      <FilterEmployeeDialog
        open={showFilterDialog}
        onOpenChange={setShowFilterDialog}
        onApplyFilters={handleApplyFilters}
        currentFilters={filters}
      />
    </div>
  );
};
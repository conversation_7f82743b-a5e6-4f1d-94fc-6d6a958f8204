import React, { useState } from 'react';
import ProtectedRoute from '@/components/ProtectedRoute';
import { Header } from '@/components/Header';
import { Dashboard } from '@/components/Dashboard';
import { PayrollPreview } from '@/components/PayrollPreview';
import { PayslipCustomizer } from '@/components/PayslipCustomizer';
import { EmployeeManagement } from '@/components/EmployeeManagement';
import { CompanyBranding } from '@/types/branding';

const Index = () => {
  const [currentTab, setCurrentTab] = useState('dashboard');
  const [companyBranding, setCompanyBranding] = useState<CompanyBranding>({
    companyName: 'TechCorp Ghana Ltd',
    address: '123 Liberation Road, Accra, Ghana',
    tin: '12345678',
    primaryColor: '#DC2626',
    secondaryColor: '#059669',
    headerStyle: 'corporate',
    includeWatermark: false,
    footerText: 'This payslip is computer generated and does not require signature.'
  });

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Header 
          currentTab={currentTab} 
          onTabChange={setCurrentTab}
        />
        
        <main className="container mx-auto px-4 py-8">
          {currentTab === 'dashboard' && <Dashboard />}
          {currentTab === 'employees' && <EmployeeManagement />}
          {currentTab === 'payroll' && <PayrollPreview branding={companyBranding} />}
          {currentTab === 'payslip-design' && (
            <PayslipCustomizer 
              currentBranding={companyBranding}
              onBrandingChange={setCompanyBranding}
            />
          )}
        </main>
      </div>
    </ProtectedRoute>
  );
};

export default Index;

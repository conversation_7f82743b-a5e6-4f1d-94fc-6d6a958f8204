import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { CompanyBranding } from "@/types/branding";
import { 
  FileText, 
  Download, 
  Send, 
  Eye,
  User,
  Banknote
} from "lucide-react";

interface PayrollPreviewProps {
  branding?: CompanyBranding;
}

export const PayrollPreview = ({ branding }: PayrollPreviewProps) => {
  const defaultBranding: CompanyBranding = {
    companyName: "ACME CORP GHANA LTD",
    address: "P.O. Box 123, Accra",
    tin: "********",
    primaryColor: "#CE1126",
    secondaryColor: "#FFD700",
    headerStyle: 'corporate',
    includeWatermark: false,
    footerText: "This payslip is computer generated and does not require signature."
  };

  const activeBranding = branding || defaultBranding;
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Payroll Preview</h2>
          <p className="text-muted-foreground">January 2024 • Preview Mode</p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="bg-ghana-gold/10 text-ghana-gold border-ghana-gold/20">
            Draft
          </Badge>
          <Button variant="outline" size="sm">
            <Eye className="w-4 h-4 mr-2" />
            Preview All
          </Button>
          <Button>Approve Payroll</Button>
        </div>
      </div>

      {/* Payroll Summary */}
      <Card className="shadow-medium">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Banknote className="w-5 h-5 text-primary" />
            <span>Payroll Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">₵2,428,500</div>
              <div className="text-sm text-muted-foreground">Gross Pay</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-ghana-red">₵524,800</div>
              <div className="text-sm text-muted-foreground">Total Deductions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-ghana-green">₵1,903,700</div>
              <div className="text-sm text-muted-foreground">Net Pay</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">247</div>
              <div className="text-sm text-muted-foreground">Employees</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sample Payslip */}
      <Card className="shadow-medium">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-primary" />
              <span>Sample Payslip Preview</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Download PDF
              </Button>
              <Button variant="outline" size="sm">
                <Send className="w-4 h-4 mr-2" />
                Send Test
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-card border rounded-lg p-6 space-y-4">
            {/* Payslip Header */}
            <div className={activeBranding.headerStyle === 'modern' 
              ? "text-center pb-4 bg-gradient-primary text-white p-6 rounded-t-lg -m-6 mb-6"
              : activeBranding.headerStyle === 'corporate'
              ? "flex items-center justify-between border-b pb-4"
              : "text-center border-b pb-4"
            }>
              <div className={activeBranding.headerStyle === 'corporate' ? "" : "text-center"}>
                <h3 className="text-lg font-bold" style={{ color: activeBranding.headerStyle === 'modern' ? 'white' : activeBranding.primaryColor }}>
                  {activeBranding.companyName}
                </h3>
                <p className="text-sm text-muted-foreground">{activeBranding.address} • TIN: {activeBranding.tin}</p>
                <p className="text-sm font-medium mt-2">PAYSLIP - JANUARY 2024</p>
              </div>
            </div>

            {/* Employee Info */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p><strong>Employee:</strong> Kwame Asante</p>
                <p><strong>ID:</strong> EMP001</p>
                <p><strong>Department:</strong> Engineering</p>
              </div>
              <div>
                <p><strong>SSNIT No:</strong> S********9</p>
                <p><strong>TIN:</strong> T987654321</p>
                <p><strong>Bank:</strong> GCB - **********</p>
              </div>
            </div>

            <Separator />

            {/* Earnings */}
            <div className="space-y-2">
              <h4 className="font-semibold text-ghana-green">EARNINGS</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>Basic Salary</span>
                  <span>₵8,000.00</span>
                </div>
                <div className="flex justify-between">
                  <span>Housing Allowance</span>
                  <span>₵2,400.00</span>
                </div>
                <div className="flex justify-between">
                  <span>Transport Allowance</span>
                  <span>₵800.00</span>
                </div>
                <div className="flex justify-between">
                  <span>Overtime</span>
                  <span>₵450.00</span>
                </div>
              </div>
              <div className="flex justify-between font-semibold border-t pt-2">
                <span>Gross Pay</span>
                <span>₵11,650.00</span>
              </div>
            </div>

            <Separator />

            {/* Deductions */}
            <div className="space-y-2">
              <h4 className="font-semibold text-ghana-red">DEDUCTIONS</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>Employee SSNIT (5.5%)</span>
                  <span>₵640.75</span>
                </div>
                <div className="flex justify-between">
                  <span>PAYE Tax</span>
                  <span>₵1,245.50</span>
                </div>
                <div className="flex justify-between">
                  <span>NHIS</span>
                  <span>₵116.50</span>
                </div>
                <div className="flex justify-between">
                  <span>Loan Repayment</span>
                  <span>₵500.00</span>
                </div>
              </div>
              <div className="flex justify-between font-semibold border-t pt-2">
                <span>Total Deductions</span>
                <span>₵2,502.75</span>
              </div>
            </div>

            <Separator />

            {/* Net Pay */}
            <div className="bg-primary/5 border border-primary/20 rounded-lg p-3">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">NET PAY</span>
                <span className="text-2xl font-bold" style={{ color: activeBranding.primaryColor }}>₵9,147.25</span>
              </div>
            </div>

            {/* Employer Contributions */}
            <div className="text-xs text-muted-foreground space-y-1">
              <p><strong>Employer Contributions:</strong></p>
              <p>SSNIT (13%): ₵1,514.50 | Pension (13%): ₵1,514.50</p>
            </div>

            {/* Footer */}
            {activeBranding.footerText && (
              <div className="text-xs text-muted-foreground text-center border-t pt-4">
                {activeBranding.footerText}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
import { useState, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { CompanyBranding } from "@/types/branding";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { FileService } from "@/services/fileService";
import { PDFService } from "@/services/pdfService";
import { supabase } from "@/integrations/supabase/client";
import { Upload, Eye, Save, Download, Palette, FileText, Check } from "lucide-react";

interface PayslipCustomizerProps {
  onBrandingChange: (branding: CompanyBranding) => void;
  currentBranding: CompanyBranding;
}

export const PayslipCustomizer = ({ onBrandingChange, currentBranding }: PayslipCustomizerProps) => {
  const { profile } = useAuth();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load saved branding from localStorage or use current branding
  const loadSavedBranding = () => {
    try {
      const saved = localStorage.getItem('payslip_branding');
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.warn('Failed to load saved branding:', error);
    }
    return currentBranding;
  };

  const [branding, setBranding] = useState<CompanyBranding>(loadSavedBranding());
  const [previewMode, setPreviewMode] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleBrandingUpdate = (field: keyof CompanyBranding, value: any) => {
    const updated = { ...branding, [field]: value };
    setBranding(updated);
    onBrandingChange(updated);
  };

  const handleSaveTemplate = async () => {
    // Use a mock company ID if none exists
    const companyId = profile?.company_id || profile?.id || 'demo-company';

    setSaving(true);
    try {
      // For now, just save to localStorage since database might not be set up
      localStorage.setItem('payslip_branding', JSON.stringify(branding));

      toast({
        title: 'Success',
        description: 'Template saved successfully to local storage',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to save template: ' + error.message,
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleUploadLogo = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const allowedTypes = ['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      toast({
        title: 'Error',
        description: 'Invalid file type. Please upload a JPEG, PNG, SVG, or WebP image.',
        variant: 'destructive',
      });
      return;
    }

    if (file.size > maxSize) {
      toast({
        title: 'Error',
        description: 'File size too large. Please upload an image smaller than 5MB.',
        variant: 'destructive',
      });
      return;
    }

    setUploading(true);
    try {
      // Convert file to base64 for demo purposes (since Supabase storage might not be set up)
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64String = e.target?.result as string;
        handleBrandingUpdate('logo', base64String);

        toast({
          title: 'Success',
          description: 'Logo uploaded successfully',
        });
        setUploading(false);
      };

      reader.onerror = () => {
        toast({
          title: 'Error',
          description: 'Failed to read file',
          variant: 'destructive',
        });
        setUploading(false);
      };

      reader.readAsDataURL(file);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to upload logo: ' + error.message,
        variant: 'destructive',
      });
      setUploading(false);
    }
  };

  const handleDownloadSample = async () => {
    try {
      const samplePayslipData = {
        employee: {
          first_name: 'Kwame',
          last_name: 'Asante',
          employee_id: 'EMP001',
          department: 'Engineering',
          position: 'Software Engineer',
          ssnit_number: 'S123456789',
          tin_number: 'T987654321',
          bank_name: 'GCB Bank',
          bank_account: '**********',
        },
        company: {
          name: branding.companyName,
          address: branding.address,
          tin: branding.tin,
          logo: branding.logo,
        },
        payroll: {
          period_start: new Date().toISOString(),
          period_end: new Date().toISOString(),
          basic_salary: 8000,
          housing_allowance: 2400,
          transport_allowance: 800,
          other_allowances: 450,
          overtime_amount: 0,
          gross_salary: 11650,
          ssnit_employee: 640.75,
          ssnit_employer: 1514.50,
          paye_tax: 1245.50,
          nhis: 116.50,
          other_deductions: 500,
          net_salary: 9147.25,
        },
        branding: branding,
      };

      await PDFService.downloadPayslip(samplePayslipData);

      toast({
        title: 'Success',
        description: 'Sample payslip downloaded successfully',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to download sample: ' + error.message,
        variant: 'destructive',
      });
    }
  };

  const headerStyles = [
    { id: 'minimal', name: 'Minimal', description: 'Clean and simple header' },
    { id: 'corporate', name: 'Corporate', description: 'Professional with logo prominence' },
    { id: 'modern', name: 'Modern', description: 'Contemporary design with gradients' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Payslip Customization</h2>
          <p className="text-muted-foreground">Design your company's payslip template</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
          >
            <Eye className="w-4 h-4 mr-2" />
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
          <Button
            onClick={handleSaveTemplate}
            disabled={saving}
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {saving ? 'Saving...' : 'Save Template'}
          </Button>
        </div>
      </div>

      {!previewMode ? (
        <Tabs defaultValue="branding" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="branding">Company Branding</TabsTrigger>
            <TabsTrigger value="design">Design & Layout</TabsTrigger>
            <TabsTrigger value="content">Content Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="branding" className="space-y-6">
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5 text-primary" />
                  <span>Company Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyName">Company Name</Label>
                    <Input
                      id="companyName"
                      value={branding.companyName}
                      onChange={(e) => handleBrandingUpdate('companyName', e.target.value)}
                      placeholder="Your Company Ltd"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tin">Tax Identification Number</Label>
                    <Input
                      id="tin"
                      value={branding.tin}
                      onChange={(e) => handleBrandingUpdate('tin', e.target.value)}
                      placeholder="12345678"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Company Address</Label>
                  <Textarea
                    id="address"
                    value={branding.address}
                    onChange={(e) => handleBrandingUpdate('address', e.target.value)}
                    placeholder="P.O. Box 123, Accra, Ghana"
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Company Logo</Label>
                  <div className="flex items-center space-x-4">
                    <div className="w-20 h-20 border-2 border-dashed border-border rounded-lg flex items-center justify-center bg-muted/50">
                      {branding.logo ? (
                        <img src={branding.logo} alt="Logo" className="w-full h-full object-contain rounded" />
                      ) : (
                        <Upload className="w-6 h-6 text-muted-foreground" />
                      )}
                    </div>
                    <div className="space-y-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploading}
                      >
                        {uploading ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                        ) : (
                          <Upload className="w-4 h-4 mr-2" />
                        )}
                        {uploading ? 'Uploading...' : 'Upload Logo'}
                      </Button>
                      {branding.logo && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleBrandingUpdate('logo', '')}
                        >
                          Remove Logo
                        </Button>
                      )}
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleUploadLogo}
                        className="hidden"
                      />
                      <p className="text-xs text-muted-foreground">
                        Max 5MB • JPG, PNG, SVG, WebP
                      </p>
                      {branding.logo && (
                        <p className="text-xs text-green-600">
                          ✓ Logo uploaded successfully
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="design" className="space-y-6">
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Palette className="w-5 h-5 text-primary" />
                  <span>Design & Colors</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        id="primaryColor"
                        value={branding.primaryColor}
                        onChange={(e) => handleBrandingUpdate('primaryColor', e.target.value)}
                        className="w-12 h-10 rounded border border-input cursor-pointer"
                      />
                      <Input
                        value={branding.primaryColor}
                        onChange={(e) => handleBrandingUpdate('primaryColor', e.target.value)}
                        placeholder="#000000"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        id="secondaryColor"
                        value={branding.secondaryColor}
                        onChange={(e) => handleBrandingUpdate('secondaryColor', e.target.value)}
                        className="w-12 h-10 rounded border border-input cursor-pointer"
                      />
                      <Input
                        value={branding.secondaryColor}
                        onChange={(e) => handleBrandingUpdate('secondaryColor', e.target.value)}
                        placeholder="#666666"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <Label>Header Style</Label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {headerStyles.map((style) => (
                      <div
                        key={style.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                          branding.headerStyle === style.id
                            ? 'border-primary bg-primary/5'
                            : 'border-border hover:border-primary/50'
                        }`}
                        onClick={() => handleBrandingUpdate('headerStyle', style.id)}
                      >
                        <div className="font-medium">{style.name}</div>
                        <div className="text-sm text-muted-foreground">{style.description}</div>
                        {branding.headerStyle === style.id && (
                          <Badge variant="secondary" className="mt-2">Selected</Badge>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="content" className="space-y-6">
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle>Content Customization</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-base">Include Watermark</Label>
                    <p className="text-sm text-muted-foreground">Add a subtle company watermark</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={branding.includeWatermark}
                    onChange={(e) => handleBrandingUpdate('includeWatermark', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="footerText">Footer Text</Label>
                  <Textarea
                    id="footerText"
                    value={branding.footerText}
                    onChange={(e) => handleBrandingUpdate('footerText', e.target.value)}
                    placeholder="This payslip is computer generated and does not require signature."
                    rows={2}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Live Preview</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadSample}
            >
              <Download className="w-4 h-4 mr-2" />
              Download Sample
            </Button>
          </div>
          <CustomPayslipPreview branding={branding} />
        </div>
      )}
    </div>
  );
};

const CustomPayslipPreview = ({ branding }: { branding: CompanyBranding }) => {
  const getHeaderStyle = () => {
    switch (branding.headerStyle) {
      case 'minimal':
        return 'text-center border-b pb-4';
      case 'corporate':
        return 'flex items-center justify-between border-b pb-4';
      case 'modern':
        return 'text-center pb-4 bg-gradient-primary text-white p-6 rounded-t-lg -m-6 mb-6';
      default:
        return 'text-center border-b pb-4';
    }
  };

  return (
    <Card className="shadow-elegant">
      <CardContent className="p-6">
        <div className="bg-card border rounded-lg p-6 space-y-4" style={{ color: branding.primaryColor }}>
          <div className={getHeaderStyle()}>
            {branding.headerStyle === 'corporate' ? (
              <>
                <div>
                  <h3 className="text-lg font-bold">{branding.companyName}</h3>
                  <p className="text-sm text-muted-foreground">{branding.address}</p>
                  <p className="text-sm text-muted-foreground">TIN: {branding.tin}</p>
                </div>
                {branding.logo && (
                  <img src={branding.logo} alt="Logo" className="w-16 h-16 object-contain" />
                )}
              </>
            ) : (
              <>
                <h3 className="text-lg font-bold">{branding.companyName}</h3>
                <p className="text-sm text-muted-foreground">{branding.address}</p>
                <p className="text-sm text-muted-foreground">TIN: {branding.tin}</p>
              </>
            )}
            <p className="text-sm font-medium mt-2">PAYSLIP - JANUARY 2024</p>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p><strong>Employee:</strong> Kwame Asante</p>
              <p><strong>ID:</strong> EMP001</p>
              <p><strong>Department:</strong> Engineering</p>
            </div>
            <div>
              <p><strong>SSNIT No:</strong> S123456789</p>
              <p><strong>TIN:</strong> T987654321</p>
              <p><strong>Bank:</strong> GCB - **********</p>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold" style={{ color: branding.secondaryColor }}>EARNINGS</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex justify-between">
                <span>Basic Salary</span>
                <span>₵8,000.00</span>
              </div>
              <div className="flex justify-between">
                <span>Housing Allowance</span>
                <span>₵2,400.00</span>
              </div>
            </div>
            <div className="flex justify-between font-semibold border-t pt-2">
              <span>Gross Pay</span>
              <span>₵11,650.00</span>
            </div>
          </div>

          <div className="bg-primary/5 border border-primary/20 rounded-lg p-3">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold">NET PAY</span>
              <span className="text-2xl font-bold" style={{ color: branding.primaryColor }}>₵9,147.25</span>
            </div>
          </div>

          {branding.footerText && (
            <div className="text-xs text-muted-foreground text-center border-t pt-4">
              {branding.footerText}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

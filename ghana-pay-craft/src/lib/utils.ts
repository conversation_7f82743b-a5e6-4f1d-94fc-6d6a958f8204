import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Currency formatting for Ghana Cedis
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-GH', {
    style: 'currency',
    currency: 'GHS',
    minimumFractionDigits: 2
  }).format(amount);
}

// Date formatting utilities
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  }).format(dateObj);
}

export function formatDateLong(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('en-GB', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  }).format(dateObj);
}

// Generate employee ID
export function generateEmployeeId(prefix: string = 'EMP'): string {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}${timestamp}${random}`;
}

// Validate Ghana TIN format
export function validateGhanaTIN(tin: string): boolean {
  // Ghana TIN format: 8-12 digits
  const tinRegex = /^\d{8,12}$/;
  return tinRegex.test(tin.replace(/\s/g, ''));
}

// Validate Ghana SSNIT number format
export function validateSSNIT(ssnit: string): boolean {
  // Ghana SSNIT format: S followed by 9 digits
  const ssnitRegex = /^S\d{9}$/;
  return ssnitRegex.test(ssnit.replace(/\s/g, ''));
}

// Calculate PAYE tax for Ghana
export function calculatePAYE(grossSalary: number): number {
  // Ghana PAYE tax brackets (simplified)
  if (grossSalary <= 365) return 0;
  if (grossSalary <= 730) return (grossSalary - 365) * 0.05;
  if (grossSalary <= 3650) return 18.25 + (grossSalary - 730) * 0.10;
  if (grossSalary <= 16425) return 310.25 + (grossSalary - 3650) * 0.175;
  if (grossSalary <= 54750) return 2545.88 + (grossSalary - 16425) * 0.25;
  return 12127.13 + (grossSalary - 54750) * 0.30;
}

// Calculate SSNIT contribution
export function calculateSSNIT(grossSalary: number, isEmployee: boolean = true): number {
  const rate = isEmployee ? 0.055 : 0.13; // 5.5% for employee, 13% for employer
  return grossSalary * rate;
}

// Calculate NHIS contribution
export function calculateNHIS(grossSalary: number): number {
  return grossSalary * 0.025; // 2.5% of gross salary
}

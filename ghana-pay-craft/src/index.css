@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 222 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    /* Ghana-inspired professional palette */
    --primary: 142 76% 36%; /* Ghana green */
    --primary-foreground: 0 0% 100%;
    
    --ghana-red: 0 72% 51%;
    --ghana-gold: 45 100% 51%;
    --ghana-green: 142 76% 36%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47.4% 11.2%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 45 100% 51%; /* Ghana gold */
    --accent-foreground: 222 84% 4.9%;

    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 142 76% 36%;
    
    /* Business gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--ghana-green)), hsl(var(--primary)));
    --gradient-accent: linear-gradient(135deg, hsl(var(--ghana-gold)), hsl(var(--accent)));
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--muted)));
    
    /* Professional shadows */
    --shadow-soft: 0 2px 10px -3px hsl(var(--foreground) / 0.1);
    --shadow-medium: 0 4px 20px -4px hsl(var(--foreground) / 0.15);
    --shadow-strong: 0 10px 40px -10px hsl(var(--primary) / 0.3);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
  
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }
  
  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }
  
  .shadow-strong {
    box-shadow: var(--shadow-strong);
  }
}